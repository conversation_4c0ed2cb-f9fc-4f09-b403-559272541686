{"name": "electron-app", "private": true, "version": "1.0.0", "type": "module", "main": "dist-electron/main.js", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint .", "preview": "vite preview", "electron:dev": "vite dev --mode development", "electron:build": "tsc && vite build && electron-builder"}, "dependencies": {"lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@electron-toolkit/utils": "^3.0.0", "@electron/remote": "^2.1.2", "@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "electron": "^29.1.1", "electron-builder": "^24.13.3", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}, "build": {"appId": "com.example.electronapp", "productName": "ElectronApp", "files": ["dist-electron/**/*", "node_modules/**/*", "build/icon.ico"], "win": {"target": "nsis", "icon": "build/icon.ico"}, "mac": {"target": "dmg", "icon": "build/icon.icns"}, "linux": {"target": "AppImage", "icon": "build/icon.png"}}}